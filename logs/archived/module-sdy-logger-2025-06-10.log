2025-06-10 16:31:49,945 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 23376 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-10 16:31:49,955 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-10 16:31:57,342 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-10 16:31:58,848 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1495 ms. Found 297 JPA repository interfaces.
2025-06-10 16:32:00,402 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-10 16:32:00,417 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-10 16:32:00,422 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-10 16:32:00,422 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-10 16:32:00,551 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-10 16:32:00,553 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 10491 ms
2025-06-10 16:32:02,440 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-10 16:32:04,814 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: 8ae855cf-b3c3-4401-a9d1-b1285b6c45f7
2025-06-10 16:32:04,817 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-10 16:32:04,915 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-10 16:32:05,032 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-10 16:32:05,108 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-10 16:32:05,520 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-10 16:32:07,974 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-10 16:32:07,975 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-10 16:32:11,327 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-10 16:32:11,359 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-10 16:32:12,400 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-10 16:32:29,817 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-10 16:32:32,345 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-10 16:32:32,512 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-10 16:32:32,559 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-10 16:32:32,586 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 43.758 seconds (process running for 45.138)
2025-06-10 16:32:42,403 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-2] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-10 16:32:42,404 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-2] Initializing Servlet 'dispatcherServlet'
2025-06-10 16:32:42,410 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-2] Completed initialization in 5 ms
2025-06-10 16:32:42,615 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-2] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-10 16:32:43,107 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-2] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-10 16:32:46,178 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-2] Movement instruction confirmation completed with status: BUSINESS_ERROR
2025-06-10 16:43:35,107 INFO o.s.o.j.AbstractEntityManagerFactoryBean [SpringApplicationShutdownHook] Closing JPA EntityManagerFactory for persistence unit 'default'
2025-06-10 16:43:35,113 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown initiated...
2025-06-10 16:43:35,121 INFO c.z.h.HikariDataSource [SpringApplicationShutdownHook] HikariPool-1 - Shutdown completed.
2025-06-10 16:43:39,956 INFO o.s.b.StartupInfoLogger [main] Starting BusinessApplication using Java 23.0.1 with PID 24232 (C:\Users\<USER>\Documents\SD1 SDY\SD1-ModuleSDY\target\classes started by AKA665 in C:\Users\<USER>\Documents\SD1 SDY)
2025-06-10 16:43:39,960 INFO o.s.b.SpringApplication [main] No active profile set, falling back to 1 default profile: "default"
2025-06-10 16:43:46,057 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Bootstrapping Spring Data JPA repositories in DEFAULT mode.
2025-06-10 16:43:47,681 INFO o.s.d.r.c.RepositoryConfigurationDelegate [main] Finished Spring Data repository scanning in 1610 ms. Found 297 JPA repository interfaces.
2025-06-10 16:43:49,338 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat initialized with port 8095 (http)
2025-06-10 16:43:49,354 INFO o.a.j.l.DirectJDKLog [main] Initializing ProtocolHandler ["http-nio-8095"]
2025-06-10 16:43:49,363 INFO o.a.j.l.DirectJDKLog [main] Starting service [Tomcat]
2025-06-10 16:43:49,363 INFO o.a.j.l.DirectJDKLog [main] Starting Servlet engine: [Apache Tomcat/10.1.31]
2025-06-10 16:43:49,487 INFO o.a.j.l.DirectJDKLog [main] Initializing Spring embedded WebApplicationContext
2025-06-10 16:43:49,489 INFO o.s.b.w.s.c.ServletWebServerApplicationContext [main] Root WebApplicationContext: initialization completed in 9426 ms
2025-06-10 16:43:51,027 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Starting...
2025-06-10 16:43:53,261 INFO c.z.h.p.HikariPool [main] HikariPool-1 - Added connection ConnectionID:1 ClientConnectionId: fbae0a52-bc8a-4ff8-839c-2054b9d9e1f9
2025-06-10 16:43:53,265 INFO c.z.h.HikariDataSource [main] HikariPool-1 - Start completed.
2025-06-10 16:43:53,349 INFO o.h.j.i.u.LogHelper [main] HHH000204: Processing PersistenceUnitInfo [name: default]
2025-06-10 16:43:53,519 INFO o.h.Version [main] HHH000412: Hibernate ORM core version 6.3.1.Final
2025-06-10 16:43:53,604 INFO o.h.c.i.RegionFactoryInitiator [main] HHH000026: Second-level cache disabled
2025-06-10 16:43:54,174 INFO o.s.o.j.p.SpringPersistenceUnitInfo [main] No LoadTimeWeaver setup: ignoring JPA class transformer
2025-06-10 16:43:57,002 WARN o.h.m.RootClass [main] HHH000038: Composite-id class does not override equals(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-10 16:43:57,003 WARN o.h.m.RootClass [main] HHH000039: Composite-id class does not override hashCode(): com.maersk.sd1.common.model.PortMasterEntityPropertyPK
2025-06-10 16:44:00,329 INFO o.h.e.t.j.p.i.JtaPlatformInitiator [main] HHH000489: No JTA platform available (set 'hibernate.transaction.jta.platform' to enable JTA platform integration)
2025-06-10 16:44:00,358 INFO o.s.o.j.AbstractEntityManagerFactoryBean [main] Initialized JPA EntityManagerFactory for persistence unit 'default'
2025-06-10 16:44:01,116 INFO o.s.d.j.r.q.QueryEnhancerFactory [main] Hibernate is in classpath; If applicable, HQL parser will be used.
2025-06-10 16:44:23,048 WARN o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration [main] spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-06-10 16:44:27,297 INFO o.s.b.a.e.w.EndpointLinksResolver [main] Exposing 1 endpoint beneath base path '/actuator'
2025-06-10 16:44:27,545 INFO o.a.j.l.DirectJDKLog [main] Starting ProtocolHandler ["http-nio-8095"]
2025-06-10 16:44:27,605 INFO o.s.b.w.e.t.TomcatWebServer [main] Tomcat started on port 8095 (http) with context path '/'
2025-06-10 16:44:27,653 INFO o.s.b.StartupInfoLogger [main] Started BusinessApplication in 48.736 seconds (process running for 49.76)
2025-06-10 16:45:48,688 INFO o.a.j.l.DirectJDKLog [http-nio-8095-exec-1] Initializing Spring DispatcherServlet 'dispatcherServlet'
2025-06-10 16:45:48,688 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Initializing Servlet 'dispatcherServlet'
2025-06-10 16:45:48,695 INFO o.s.w.s.FrameworkServlet [http-nio-8095-exec-1] Completed initialization in 6 ms
2025-06-10 16:45:48,869 INFO c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Received confirm movement instruction request: ConfirmMoveInstructionInput.Root(prefix=ConfirmMoveInstructionInput.Prefix(input=ConfirmMoveInstructionInput.Input(moveInstructionId=1812, userAlias=<EMAIL>)))
2025-06-10 16:45:49,329 INFO c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Starting confirm move instruction for ID: 1812 by user: <EMAIL>
2025-06-10 16:45:49,705 WARN o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-1] SQL Error: 207, SQLState: S0001
2025-06-10 16:45:49,706 ERROR o.h.e.j.s.SqlExceptionHelper [http-nio-8095-exec-1] Invalid column name 'correo_enviado_restaurar'.
2025-06-10 16:45:49,720 ERROR c.m.s.s.s.ConfirmMoveInstructionService [http-nio-8095-exec-1] Error confirming movement instruction
org.springframework.dao.InvalidDataAccessResourceUsageException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,c1_0.empresa_id,c1_0.abreviatura,c1_0.direccion,c1_0.unidad_negocio_id,c1_0.cat_empresa_origen_creacion,c1_0.tipo_documento,c1_0.razon_comercial,c1_0.empresa_alias,c1_0.linea_credito_actual,c1_0.documento,c1_0.latitud,c1_0.razon_social,c1_0.longitud,c1_0.correo,c1_0.fecha_modificacion,c1_0.usuario_modificacion_id,c1_0.telefono,c1_0.tipo_regimen,c1_0.fecha_registro,c1_0.usuario_registro_id,c1_0.estado,c1_0.suspendido,c1_0.tipo_contribuyente,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 left join ges.empresa c1_0 on c1_0.empresa_id=u1_0.empresa_id where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]; SQL [n/a]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:277)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:160)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:136)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:223)
	at jdk.proxy2/jdk.proxy2.$Proxy212.findByAlias(Unknown Source)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService.confirmMoveInstruction(ConfirmMoveInstructionService.java:57)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:355)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: org.hibernate.exception.SQLGrammarException: JDBC exception executing SQL [select u1_0.usuario_id,u1_0.contador_login,u1_0.id,u1_0.adjunto_foto_id,c1_0.empresa_id,c1_0.abreviatura,c1_0.direccion,c1_0.unidad_negocio_id,c1_0.cat_empresa_origen_creacion,c1_0.tipo_documento,c1_0.razon_comercial,c1_0.empresa_alias,c1_0.linea_credito_actual,c1_0.documento,c1_0.latitud,c1_0.razon_social,c1_0.longitud,c1_0.correo,c1_0.fecha_modificacion,c1_0.usuario_modificacion_id,c1_0.telefono,c1_0.tipo_regimen,c1_0.fecha_registro,c1_0.usuario_registro_id,c1_0.estado,c1_0.suspendido,c1_0.tipo_contribuyente,u1_0.fecha_cambio_clave,u1_0.apellido_paterno,u1_0.clave,u1_0.correo,u1_0.fecha_modificacion,u1_0.usuario_modificacion_id,u1_0.nombres,u1_0.persona_id,u1_0.fecha_registro,u1_0.usuario_registro_id,u1_0.correo_enviado_restaurar,u1_0.apellido_materno,u1_0.estado from seg.usuario u1_0 left join ges.empresa c1_0 on c1_0.empresa_id=u1_0.empresa_id where u1_0.id=?] [Invalid column name 'correo_enviado_restaurar'.] [n/a]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:91)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:94)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:258)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.getResultSet(DeferredResultSetAccess.java:164)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.advanceNext(JdbcValuesResultSetImpl.java:218)
	at org.hibernate.sql.results.jdbc.internal.JdbcValuesResultSetImpl.processNext(JdbcValuesResultSetImpl.java:98)
	at org.hibernate.sql.results.jdbc.internal.AbstractJdbcValues.next(AbstractJdbcValues.java:19)
	at org.hibernate.sql.results.internal.RowProcessingStateStandardImpl.next(RowProcessingStateStandardImpl.java:66)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:200)
	at org.hibernate.sql.results.spi.ListResultsConsumer.consume(ListResultsConsumer.java:33)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.doExecuteQuery(JdbcSelectExecutorStandardImpl.java:361)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.executeQuery(JdbcSelectExecutorStandardImpl.java:168)
	at org.hibernate.sql.exec.internal.JdbcSelectExecutorStandardImpl.list(JdbcSelectExecutorStandardImpl.java:93)
	at org.hibernate.sql.exec.spi.JdbcSelectExecutor.list(JdbcSelectExecutor.java:31)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.lambda$new$0(ConcreteSqmSelectQueryPlan.java:109)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.withCacheableSqmInterpretation(ConcreteSqmSelectQueryPlan.java:305)
	at org.hibernate.query.sqm.internal.ConcreteSqmSelectQueryPlan.performList(ConcreteSqmSelectQueryPlan.java:246)
	at org.hibernate.query.sqm.internal.QuerySqmImpl.doList(QuerySqmImpl.java:509)
	at org.hibernate.query.spi.AbstractSelectionQuery.list(AbstractSelectionQuery.java:427)
	at org.hibernate.query.spi.AbstractSelectionQuery.getSingleResult(AbstractSelectionQuery.java:559)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution$SingleEntityExecution.doExecute(JpaQueryExecution.java:223)
	at org.springframework.data.jpa.repository.query.JpaQueryExecution.execute(JpaQueryExecution.java:92)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.doExecute(AbstractJpaQuery.java:152)
	at org.springframework.data.jpa.repository.query.AbstractJpaQuery.execute(AbstractJpaQuery.java:140)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:169)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:148)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:379)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:138)
	... 73 common frames omitted
Caused by: com.microsoft.sqlserver.jdbc.SQLServerException: Invalid column name 'correo_enviado_restaurar'.
	at com.microsoft.sqlserver.jdbc.SQLServerException.makeFromDatabaseError(SQLServerException.java:270)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.getNextResult(SQLServerStatement.java:1735)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.doExecutePreparedStatement(SQLServerPreparedStatement.java:675)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement$PrepStmtExecCmd.doExecute(SQLServerPreparedStatement.java:594)
	at com.microsoft.sqlserver.jdbc.TDSCommand.execute(IOBuffer.java:7745)
	at com.microsoft.sqlserver.jdbc.SQLServerConnection.executeCommand(SQLServerConnection.java:4391)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeCommand(SQLServerStatement.java:276)
	at com.microsoft.sqlserver.jdbc.SQLServerStatement.executeStatement(SQLServerStatement.java:246)
	at com.microsoft.sqlserver.jdbc.SQLServerPreparedStatement.executeQuery(SQLServerPreparedStatement.java:509)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeQuery(ProxyPreparedStatement.java:52)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeQuery(HikariProxyPreparedStatement.java)
	at org.hibernate.sql.results.jdbc.internal.DeferredResultSetAccess.executeQuery(DeferredResultSetAccess.java:240)
	... 105 common frames omitted
2025-06-10 16:45:50,166 ERROR c.m.s.s.c.ConfirmMoveInstructionController [http-nio-8095-exec-1] Error processing confirm movement instruction request
org.springframework.transaction.UnexpectedRollbackException: Transaction silently rolled back because it has been marked as rollback-only
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.processCommit(AbstractPlatformTransactionManager.java:804)
	at org.springframework.transaction.support.AbstractPlatformTransactionManager.commit(AbstractPlatformTransactionManager.java:758)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.commitTransactionAfterReturning(TransactionAspectSupport.java:663)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:413)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:768)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:720)
	at com.maersk.sd1.sdy.service.ConfirmMoveInstructionService$$SpringCGLIB$$0.confirmMoveInstruction(<generated>)
	at com.maersk.sd1.sdy.controller.ConfirmMoveInstructionController.confirmMoveInstruction(ConfirmMoveInstructionController.java:63)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)
	at java.base/java.lang.Thread.run(Thread.java:1575)
