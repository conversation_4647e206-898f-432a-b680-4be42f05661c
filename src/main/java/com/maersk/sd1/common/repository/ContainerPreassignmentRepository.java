package com.maersk.sd1.common.repository;


import com.maersk.sd1.common.model.ContainerPreassignment;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;

import java.util.List;
import java.util.Optional;

public interface ContainerPreassignmentRepository extends JpaRepository<ContainerPreassignment, Integer> {
    @Query("select c from ContainerPreassignment c where c.cargoDocumentDetail.id = :cargoDocumentDetailId and c.active = true")
    ContainerPreassignment findByCargoDocumentDetailIdAndActiveTrue(@Param("cargoDocumentDetailId") Integer cargoDocumentDetailId);

    @Query("""
            select c from ContainerPreassignment c
            where c.bookingDetail.id = :bookingDetailId and c.container.id = :containerId and c.active = true
            order by c.registrationDate DESC""")
    ContainerPreassignment findByBookingDetailIdAndContainerIdAndActiveTrueOrderByRegistrationDateDesc(@Param("bookingDetailId") Integer bookingDetailId, @Param("containerId") Integer containerId);

    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM EirDocumentCargoDetail edcd " +
            "    JOIN edcd.eir e " +
            "    WHERE edcd.id = p.cargoDocumentDetail.id " +
            "    AND e.catMovement.id = :isGateOut " +
            "    AND e.catEmptyFull.id = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByPreAssingment(@Param("bookingId") Integer bookingId,
                                                      @Param("containerId") Integer containerId,
                                                      @Param("isGateOut") Integer isGateOut,
                                                      @Param("isEmpty") Integer isEmpty);

    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "JOIN CargoDocumentDetail dcd ON p.container.id = dcd.container.id AND p.bookingDetail.id = dcd.bookingDetail.id " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM EirDocumentCargoDetail edcd " +
            "    JOIN edcd.eir e " +
            "    WHERE edcd.cargoDocumentDetail.id = dcd.id " +
            "    AND e.catMovement.id = :isGateOut " +
            "    AND e.catEmptyFull.id = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "AND dcd.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByDocCargoDetail(@Param("bookingId") Integer bookingId,
                                                       @Param("containerId") Integer containerId,
                                                       @Param("isGateOut") Integer isGateOut,
                                                       @Param("isEmpty") Integer isEmpty);

    @Query("SELECT b.bookingNumber FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "LEFT JOIN Eir e ON p.container.id = e.container.id " +
            "AND e.subBusinessUnit.id = b.subBusinessUnit.id " +
            "AND e.vesselProgrammingDetail.id = b.vesselProgrammingDetail.id " +
            "AND e.catMovement = :isGateOut " +
            "AND e.catEmptyFull = :isEmpty " +
            "AND e.active = true " +
            "WHERE p.container.id = :containerId " +
            "AND b.subBusinessUnit.id = :subBusinessUnitId " +
            "AND e.id IS NULL " +
            "AND p.active = true " +
            "AND bd.active = true " +
            "AND b.active = true")
    String findBookingNumberFromPreassigmentEIR(@Param("containerId") Integer containerId,
                                                @Param("subBusinessUnitId") Integer subBusinessUnitId,
                                                @Param("isGateOut") Integer isGateOut,
                                                @Param("isEmpty") Integer isEmpty);



    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerId AND p.active = true")
    List<ContainerPreassignment> findByContainerId(@Param("containerId") Integer containerId);


    @Query(value = "SELECT C FROM ContainerPreassignment C " +
            "WHERE C.cargoDocumentDetail.id = :cargoDocumentDetailId " +
            "AND C.catOriginPreassignment.id = :isPreallocation " +
            "AND C.active = TRUE " +
            "ORDER BY C.registrationDate DESC")
    List<ContainerPreassignment> findByCargoDocumentDetailIdAndCatOriginPreassignment(Integer cargoDocumentDetailId, Integer isPreallocation);


    @Query("SELECT cp FROM ContainerPreassignment cp WHERE cp.container.id IN :containerIds AND cp.active = true")
    List<ContainerPreassignment> findByContainerIdsAndActive(@Param("containerIds") List<Integer> containerIds);


    @Query("SELECT COUNT(p) FROM ContainerPreassignment p "
            + "WHERE p.bookingDetail.id = :bookingDetailId "
            + "  AND p.active = true")
    long countActivePreassignmentsByBookingDetail(@Param("bookingDetailId") Integer bookingDetailId);

    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerNumber AND p.bookingDetail.booking.id = :booking AND p.active = true")
    List<ContainerPreassignment> findActivePreassignmentsByContainerAndBooking(
            @Param("containerNumber") Integer containerNumber,
            @Param("booking") Integer booking);

    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id = :containerNumber AND p.bookingDetail.booking.id != :booking AND p.active = true")
    List<ContainerPreassignment> findActivePreassignmentsByContainerAndNotBooking(
            @Param("containerNumber") Integer containerNumber,
            @Param("booking") Integer booking);


    List<ContainerPreassignment> findByBookingDetailIdAndActiveOrderByRegistrationDateAsc(Integer bookingDetailId, Boolean active);


    @Query("SELECT p.id FROM ContainerPreassignment p " +
            "JOIN p.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE b.id = :bookingId " +
            "AND p.container.id = :containerId " +
            "AND NOT EXISTS ( " +
            "    SELECT 1 FROM Eir e " +
            "    JOIN EirDocumentCargoDetail edcd " +
            "    WHERE edcd.cargoDocumentDetail.id = p.cargoDocumentDetail.id " +
            "    AND e.catMovement = :isGateOut " +
            "    AND e.catEmptyFull = :isEmpty " +
            "    AND edcd.active = true " +
            "    AND e.active = true " +
            ") " +
            "AND b.active = true " +
            "AND bd.active = true " +
            "AND p.active = true " +
            "ORDER BY p.registrationDate DESC")
    Optional<Integer> findPreAssigmentByBookingIdAndContainerId(@Param("bookingId") Integer bookingId,
                                                                @Param("containerId") Integer containerId,
                                                                @Param("isGateOut") Integer isGateOut,
                                                                @Param("isEmpty") Integer isEmpty);
    @Query("SELECT p FROM ContainerPreassignment p WHERE p.container.id IN :containerIds AND p.active = true")
    List<ContainerPreassignment> findByContainerIds(@Param("containerIds") List<Integer> containerIds);



    @Query("SELECT cp FROM ContainerPreassignment cp " +
            "JOIN cp.container c " +
            "JOIN cp.bookingDetail bd " +
            "JOIN bd.booking b " +
            "WHERE cp.active = true AND b.active = true AND bd.active = true " +
            "AND cp.container.id = :containerId")
    List<ContainerPreassignment> findActivePreassignmentsByContainerId(@Param("containerId") Integer containerId);

    @Query("SELECT pc.container.id FROM ContainerPreassignment pc " +
            "JOIN pc.bookingDetail bd " +
            "JOIN bd.booking b " +
            "LEFT JOIN CargoDocumentDetail dcd ON dcd.container.id = pc.container.id AND dcd.bookingDetail.id = bd.id AND dcd.active = TRUE " +
            "LEFT JOIN EirDocumentCargoDetail edcd ON edcd.cargoDocumentDetail.id = dcd.id AND edcd.active = TRUE " +
            "LEFT JOIN Eir e ON (e.id = edcd.eir.id AND e.catMovement.id = :gateOutMovementType AND e.catEmptyFull.id = :isMty AND e.active = TRUE) " +
            "WHERE b.id <> :bookingId " +
            "  AND b.subBusinessUnit.id = :businessUnitParentId " +
            "  AND pc.active = TRUE AND bd.active = TRUE AND b.active = TRUE " +
            "  AND edcd.id IS NULL ")
    List<Integer> findPreassignedContainersOtherBooking(@Param("bookingId") Integer bookingId,
                                                        @Param("businessUnitParentId") Integer businessUnitParentId,
                                                        @Param("gateOutMovementType") Integer gateOutMovementType,
                                                        @Param("isMty") Integer isMty);

    @Query("SELECT pc FROM ContainerPreassignment pc "
            + " JOIN FETCH pc.bookingDetail bd "
            + " JOIN FETCH bd.booking b "
            + " WHERE pc.active = true "
            + "   AND bd.active = true "
            + "   AND b.active = true "
            + "   AND b.id = :bookingId "
            + "   AND pc.container.id IN :containerIds ")
    Optional<List<ContainerPreassignment>> findPreassignmentsOfBooking(@Param("bookingId") Integer bookingId,
                                                             @Param("containerIds") List<Integer> containerIds);

    @Query(value = """
            SELECT
                PRS.contenedor_id AS container_id,
                BKG.numero_booking AS booking_number
            FROM (
                SELECT
                    PRS.contenedor_id,
                    PRS.booking_detalle_id,
                    ROW_NUMBER() OVER (PARTITION BY PRS.contenedor_id ORDER BY PRS.fecha_preasignacion DESC) AS rn
                FROM sds.preasignacion_contenedor PRS (NOLOCK)
                INNER JOIN sds.documento_carga_detalle DCD (NOLOCK)
                    ON DCD.booking_detalle_id = PRS.booking_detalle_id
                INNER JOIN sds.booking_detalle BDT (NOLOCK)
                    ON BDT.booking_detalle_id = PRS.booking_detalle_id
                INNER JOIN sds.booking BKG (NOLOCK)
                    ON (BKG.booking_id = BDT.booking_id AND BKG.sub_unidad_negocio_id = :subBusinessUnitId)
                LEFT OUTER JOIN sde.eir_documento_carga_detalle EDCD (NOLOCK)
                    ON (EDCD.documento_carga_detalle_id = DCD.documento_carga_detalle_id AND EDCD.activo = 1)
                LEFT OUTER JOIN sde.eir EIR (NOLOCK)
                    ON (EIR.eir_id = EDCD.eir_id AND EIR.cat_movimiento_id = :isGateOut AND EIR.cat_empty_full_id = :isEmpty AND EIR.activo = 1)
                WHERE PRS.contenedor_id IN :containerIds
                    AND EDCD.eir_documento_carga_detalle_id IS NULL
                    AND EIR.eir_id IS NULL
                    AND PRS.activo = 1
                    AND DCD.activo = 1
                    AND BDT.activo = 1
                    AND BKG.activo = 1
            ) PRS
            INNER JOIN sds.booking_detalle BDT (NOLOCK) ON BDT.booking_detalle_id = PRS.booking_detalle_id
            INNER JOIN sds.booking BKG (NOLOCK) ON BKG.booking_id = BDT.booking_id
            WHERE PRS.rn = 1
            """, nativeQuery = true)
    List<Object[]> findLatestPreallocationBookingNumbers(
            @Param("containerIds") List<Integer> containerIds,
            @Param("subBusinessUnitId") Integer subBusinessUnitId,
            @Param("isGateOut") Integer isGateOut,
            @Param("isEmpty") Integer isEmpty);

}