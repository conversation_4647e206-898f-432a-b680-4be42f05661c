package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionInput;
import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput;
import com.maersk.sd1.sdy.service.ConfirmMoveInstructionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/sdy/movement-instruction")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Movement Instruction", description = "Movement Instruction Management APIs")
public class ConfirmMoveInstructionController {

    private final ConfirmMoveInstructionService confirmMoveInstructionService;

    @Operation(
        summary = "Confirm Movement Instruction",
        description = "Confirms and executes a movement instruction by updating container locations and instruction status"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Movement instruction confirmed successfully",
            content = @Content(schema = @Schema(implementation = ConfirmMoveInstructionOutput.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input parameters",
            content = @Content(schema = @Schema(implementation = String.class))
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Movement instruction not found",
            content = @Content(schema = @Schema(implementation = String.class))
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error",
            content = @Content(schema = @Schema(implementation = String.class))
        )
    })
    @PostMapping("/confirm")
    public ResponseEntity<ConfirmMoveInstructionOutput> confirmMoveInstruction(
            @Parameter(description = "Movement instruction confirmation request", required = true)
            @Valid @RequestBody ConfirmMoveInstructionInput.Root request) {
        
        log.info("Received confirm movement instruction request: {}", request);
        
        try {
            ConfirmMoveInstructionInput.Input input = request.getPrefix().getInput();
            ConfirmMoveInstructionOutput output = confirmMoveInstructionService.confirmMoveInstruction(input);
            
            log.info("Movement instruction confirmation completed with status: {}", output.getRespEstado());
            return ResponseEntity.ok(output);
            
        } catch (Exception e) {
            log.error("Error processing confirm movement instruction request", e);
            
            ConfirmMoveInstructionOutput errorOutput = new ConfirmMoveInstructionOutput();
            errorOutput.setRespEstado("INTERNAL_ERROR");
            errorOutput.setRespMensaje("Internal server error: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorOutput);
        }
    }

    @Operation(
        summary = "Confirm Movement Instruction (Alternative Endpoint)",
        description = "Alternative endpoint for confirming movement instructions with simplified input"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Movement instruction confirmed successfully",
            content = @Content(schema = @Schema(implementation = ConfirmMoveInstructionOutput.class))
        ),
        @ApiResponse(
            responseCode = "400",
            description = "Invalid input parameters"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Movement instruction not found"
        ),
        @ApiResponse(
            responseCode = "500",
            description = "Internal server error"
        )
    })
    @PostMapping("/confirm/{moveInstructionId}")
    public ResponseEntity<ConfirmMoveInstructionOutput> confirmMoveInstructionById(
            @Parameter(description = "Movement instruction ID", required = true)
            @PathVariable Integer moveInstructionId,
            
            @Parameter(description = "User alias", required = true)
            @RequestParam String userAlias) {
        
        log.info("Received confirm movement instruction request for ID: {} by user: {}", 
                moveInstructionId, userAlias);
        
        try {
            ConfirmMoveInstructionInput.Input input = new ConfirmMoveInstructionInput.Input();
            input.setMoveInstructionId(moveInstructionId);
            input.setUserAlias(userAlias);
            
            ConfirmMoveInstructionOutput output = confirmMoveInstructionService.confirmMoveInstruction(input);
            
            log.info("Movement instruction confirmation completed with status: {}", output.getRespEstado());
            return ResponseEntity.ok(output);
            
        } catch (Exception e) {
            log.error("Error processing confirm movement instruction request", e);
            
            ConfirmMoveInstructionOutput errorOutput = new ConfirmMoveInstructionOutput();
            errorOutput.setRespEstado("INTERNAL_ERROR");
            errorOutput.setRespMensaje("Internal server error: " + e.getMessage());
            
            return ResponseEntity.internalServerError().body(errorOutput);
        }
    }

    @Operation(
        summary = "Get Movement Instruction Status",
        description = "Retrieves the current status of a movement instruction"
    )
    @ApiResponses(value = {
        @ApiResponse(
            responseCode = "200",
            description = "Status retrieved successfully"
        ),
        @ApiResponse(
            responseCode = "404",
            description = "Movement instruction not found"
        )
    })
    @GetMapping("/status/{moveInstructionId}")
    public ResponseEntity<String> getMovementInstructionStatus(
            @Parameter(description = "Movement instruction ID", required = true)
            @PathVariable Integer moveInstructionId) {
        
        log.info("Received get status request for movement instruction ID: {}", moveInstructionId);
        
        try {
            // This would typically call a service method to get the status
            // For now, returning a placeholder response
            return ResponseEntity.ok("Status check not implemented yet");
            
        } catch (Exception e) {
            log.error("Error getting movement instruction status", e);
            return ResponseEntity.internalServerError().body("Error: " + e.getMessage());
        }
    }
}
