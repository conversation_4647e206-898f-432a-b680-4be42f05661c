package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.dto.ResponseController;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmInput;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput;
import com.maersk.sd1.sdy.service.ConfirmMoveInstructionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequiredArgsConstructor
@RequestMapping("/ModuleSDY/module/sdy/SDYMovementInstructionServiceImp")
public class ConfirmMoveInstructionController {

    private static final Logger logger = LogManager.getLogger(ConfirmMoveInstructionController.class);

    private final ConfirmMoveInstructionService confirmMoveInstructionService;

    @PostMapping("/sdyconfirmarInstruccionMovimiento")
    public ResponseEntity<ResponseController<MovementInstructionConfirmOutput>> confirmMoveInstruction(
            @RequestBody @Valid MovementInstructionConfirmInput.Root request) {
        try {
            if (request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Invalid request structure");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request structure"));
            }

            logger.info("Request received for confirm move instruction: {}", request);
            MovementInstructionConfirmInput.Input input = request.getPrefix().getInput();
            MovementInstructionConfirmOutput result = confirmMoveInstructionService.confirmMoveInstruction(input);
            return ResponseEntity.ok(new ResponseController<>(result));
        } catch (Exception e) {
            logger.error("An error occurred while processing confirm move instruction request.", e);
            MovementInstructionConfirmOutput errorOutput = new MovementInstructionConfirmOutput();
            errorOutput.setRespEstado("INTERNAL_ERROR");
            errorOutput.setRespMensaje(e.getMessage());
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(errorOutput));
        }
    }
}
