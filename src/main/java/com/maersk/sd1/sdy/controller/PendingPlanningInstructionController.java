package com.maersk.sd1.sdy.controller;

import com.maersk.sd1.common.controller.dto.ResponseController;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionInput;
import com.maersk.sd1.sdy.dto.PendingPlanningInstructionOutput;
import com.maersk.sd1.sdy.service.PendingPlanningInstructionService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("/ModuleSDY/module/sdy/SDYPatioServiceImp")
public class PendingPlanningInstructionController {

    private static final Logger logger = LogManager.getLogger(PatioEditController.class);

    private final PendingPlanningInstructionService pendingPlanningInstructionService;

    @PostMapping("/test")
    public ResponseEntity<ResponseController<List<PendingPlanningInstructionOutput>>> editYard(
            @RequestBody @Valid PendingPlanningInstructionInput.Root request) {
        try {

            if(request == null || request.getPrefix() == null || request.getPrefix().getInput() == null) {
                logger.error("Invalid request");
                return ResponseEntity.badRequest().body(new ResponseController<>("Invalid request"));
            }

            logger.info("Request received for patioEditar: {}", request);
            PendingPlanningInstructionInput.Input input = request.getPrefix().getInput();
            List<PendingPlanningInstructionOutput> output = pendingPlanningInstructionService.getPendingPlanningInstructions(input);
            return ResponseEntity.ok(new ResponseController<>(output));
        } catch (Exception e) {
            logger.error("An error occurred while editing the yard.", e);
            PendingPlanningInstructionOutput output = new PendingPlanningInstructionOutput();
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(new ResponseController<>(List.of(output)));
        }
    }

}
