package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ConfirmMoveInstructionInput {

    @Data
    public static class Input {

        @JsonProperty("move_instruction_id")
        @NotNull(message = "move_instruction_id cannot be null")
        private Integer moveInstructionId;

        @JsonProperty("user_alias")
        @NotNull(message = "user_alias cannot be null")
        private String userAlias;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private Prefix prefix;
    }
}
