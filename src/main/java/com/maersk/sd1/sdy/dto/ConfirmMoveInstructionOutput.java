package com.maersk.sd1.sdy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class ConfirmMoveInstructionOutput {
    private Integer instruccionMovimientoId;
    private Integer ubicacionDestinoId;
    private Integer ubicacion40DestinoId;
    private Integer origenBloqueId;
    private String origenBloque;
    private String origenColumna;
    private String origenFila;
    private Integer origenNivel;
    private String origenBloque40;
    private String origenColumna40;
    private String origenFila40;
    private Integer origenNivel40;
    private Integer destinoBloqueId;
    private String destinoBloque;
    private String destinoColumna;
    private String destinoFila;
    private Integer destinoNivel;
    private String destinoBloque40;
    private String destinoColumna40;
    private String destinoFila40;
    private Integer destinoNivel40;
    private String contenedorTamano;
    private Integer instruccionMovimientoEstadoId;
    private String instruccionMovimientoEstadoCodigo;
    private String instruccionMovimientoEstadoNombre;
    private String bloqueTipo;
    private String respEstado;
    private String respMensaje;
}