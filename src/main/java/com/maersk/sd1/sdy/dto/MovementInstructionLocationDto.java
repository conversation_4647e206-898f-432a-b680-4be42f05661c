package com.maersk.sd1.sdy.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

@AllArgsConstructor
@Data
public class MovementInstructionLocationDto {
    private Integer blockIdOrigin;
    private Integer cellIdOrigin;
    private Integer levelIdOrigin;
    private Integer block40IdOrigin;
    private Integer cell40IdOrigin;
    private Integer level40IdOrigin;
    private Integer blockIdDestination;
    private Integer cellIdDestination;
    private Integer levelIdDestination;
    private Integer block40IdDestination;
    private Integer celda40IdDestino;
    private Integer nivel40IdDestino;
    private Integer containerId;
    private String statusMovementInstructionCode;
    private String statusMovementInstructionDescription;
    private String blockDestinationType;
    private String blockOriginType;
    private String blockDestinationCode;
}