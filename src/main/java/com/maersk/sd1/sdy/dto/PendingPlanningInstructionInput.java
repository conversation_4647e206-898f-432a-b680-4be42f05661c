package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.UtilityClass;

@UtilityClass
public class PendingPlanningInstructionInput {

    @Data
    public static class Input {
        @JsonProperty("unidad_negocio_id")
        @NotNull
        private Integer localBusinessUnitId;

        @JsonProperty("numero_contenedor")
        @NotNull
        private String containerNumber;
    }

    @Data
    public static class Prefix {
        @JsonProperty("F")
        private PendingPlanningInstructionInput.Input input;
    }

    @Data
    public static class Root {
        @JsonProperty("SDY")
        private PendingPlanningInstructionInput.Prefix prefix;
    }

}
