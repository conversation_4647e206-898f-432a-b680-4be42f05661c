package com.maersk.sd1.sdy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class PendingPlanningInstructionOutput {

    @JsonProperty("secuencia")
    private Integer sequence;

    @JsonProperty("numeroContenedor")
    private String containerNumber;

    @JsonProperty("ubicacionInicial")
    private UpdateLocationInputDTO.ContainerLocationValue initialLocation;

    @JsonProperty("ubicacionFinal")
    private UpdateLocationInputDTO.ContainerLocationValue finalLocation;

    @JsonProperty("tamanoContenedor")
    private Integer containerSize;

    @JsonProperty("cola_codigo")
    private String queueCode;

    @JsonProperty("cola_descripcion")
    private String queueDescription;

    @JsonProperty("cola_trabajo_recurso_id")
    private Integer queueResourceId;

    @JsonProperty("patio_codigo")
    private String yardCode;

    @JsonProperty("patio_nombre")
    private String yardName;

    @JsonProperty("instruccion_movimiento_id")
    private Integer movementInstructionId;

    @JsonProperty("tipo_movimiento")
    private String movementType;

    @JsonProperty("cat_familia_desc")
    private String familyDescription;

    @JsonProperty("cat_clase_desc")
    private String classDescription;

    @JsonProperty("cat_condicion_contenedor")
    private String containerCondition;

    @JsonProperty("cola_trabajo_id")
    private Integer workQueueId;

    @JsonProperty("placa_vehiculo")
    private String vehiclePlate;

    @JsonProperty("requiere_camion")
    private Boolean requiresTruck;

    @JsonProperty("comentario")
    private String comment;

    @JsonProperty("cat_empty_full_id")
    private Integer emptyFullId;

    @JsonProperty("cat_empty_full_desc")
    private String emptyFullDescription;

    @JsonProperty("cat_tamano_id")
    private Integer sizeCategoryId;

    @JsonProperty("cat_familia_id")
    private Integer familyCategoryId;

    @JsonProperty("cat_tipo_id")
    private Integer typeCategoryId;

    @JsonProperty("queue_resource_alias")
    private String queueResourceAlias;

    @JsonProperty("queue_resource_name")
    private String queueResourceName;

    @JsonProperty("chassis_number")
    private String chassisNumber;

    @JsonProperty("vehicle_plate")
    private String altVehiclePlate;

    @JsonProperty("vehicle_company")
    private String vehicleCompany;

    @JsonProperty("eir_id")
    private Integer eirId;

    @JsonProperty("cat_clase_id")
    private Integer classCategoryId;

    @JsonProperty("cat_tipo_contenedor_desc")
    private String containerTypeDescription;

    @JsonProperty("restriction_reasons")
    private String restrictionReasons;

    @JsonProperty("last_preallocation_booking_number")
    private String lastPreallocationBookingNumber;

    @JsonProperty("parent_movement_instruction_id")
    private Integer parentMovementInstructionId;

    @JsonProperty("document_number")
    private String documentNumber;
}
