package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmInput;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

@RequiredArgsConstructor
@Service
public class ConfirmMoveInstructionService {

    private static final Logger logger = LogManager.getLogger(ConfirmMoveInstructionService.class);

    private final MovementInstructionRepository movementInstructionRepository;
    private final YardRepository yardRepository;
    private final CatalogRepository catalogRepository;
    private final ContainerLocationRepository containerLocationRepository;

    private static final String BUSINESS_ERROR = "BUSINESS_ERROR";
    private static final String INTERNAL_ERROR = "INTERNAL_ERROR";
    private static final String SUCCESS = "OK";

    @Transactional
    public MovementInstructionConfirmOutput confirmMoveInstruction(MovementInstructionConfirmInput.Input input) {
        String respStatus = null;
        String respMessage = null;

        try {
            logger.info("Confirming move instruction with ID: {} by user: {}", input.getMovementInstructionId(), input.getUserAlias());

            // Get user ID from alias
            Integer userRegisterId = getUserIdFromAlias(input.getUserAlias());
            if (userRegisterId == null) {
                return buildErrorOutput(input.getMovementInstructionId(), BUSINESS_ERROR, "User not found: " + input.getUserAlias());
            }

            // Get confirmed status ID
            Integer statusConfirmed = getConfirmedStatusId();
            if (statusConfirmed == null) {
                return buildErrorOutput(input.getMovementInstructionId(), BUSINESS_ERROR, "Confirmed status not found");
            }

            // Get movement instruction with all related data
            MovementInstruction instruction = getMovementInstructionWithDetails(input.getMovementInstructionId());
            if (instruction == null) {
                return buildErrorOutput(input.getMovementInstructionId(), BUSINESS_ERROR, "Movement instruction not found");
            }

            // Validate instruction status
            String statusCode = instruction.getCatStatus().getCode();
            if (!isValidStatusForConfirmation(statusCode)) {
                String statusDescription = instruction.getCatStatus().getDescription();
                return buildErrorOutput(input.getMovementInstructionId(), BUSINESS_ERROR,
                    "The movement instruction is in status " + statusDescription + ". Cannot confirm an instruction in CREATED, TO BE ATTENDED, IN PROGRESS or ON HOLD status");
            }

            // Update instruction status
            updateInstructionStatus(instruction, statusConfirmed, userRegisterId);

            // Handle container location updates based on block types
            handleContainerLocationUpdates(instruction, userRegisterId);

            respStatus = SUCCESS;
            respMessage = "Movement instruction confirmed successfully";

        } catch (Exception ex) {
            logger.error("Error confirming movement instruction", ex);
            respStatus = INTERNAL_ERROR;
            respMessage = ex.getMessage();
        }

        return buildFinalOutput(input.getMovementInstructionId(), respStatus, respMessage);
    }

    private Integer getUserIdFromAlias(String userAlias) {
        return yardRepository.findUsuarioIdByAlias(userAlias);
    }

    private Integer getConfirmedStatusId() {
        // Get status 'EXE' from parent 'EMI'
        return catalogRepository.findMovementId("EXE", "EMI");
    }

    private MovementInstruction getMovementInstructionWithDetails(Integer instructionId) {
        Optional<MovementInstruction> instruction = movementInstructionRepository.findById(instructionId);
        return instruction.orElse(null);
    }

    private boolean isValidStatusForConfirmation(String statusCode) {
        // Valid statuses: DP (Dispatched), IP (In Progress), OH (On Hold)
        List<String> validStatuses = List.of("DP", "IP", "OH");
        return validStatuses.contains(statusCode);
    }

    private void updateInstructionStatus(MovementInstruction instruction, Integer statusConfirmed, Integer userRegisterId) {
        instruction.setCatStatus(Catalog.builder().id(statusConfirmed).build());
        instruction.setModificationUser(User.builder().id(userRegisterId).build());
        instruction.setModificationDate(LocalDateTime.now());
        movementInstructionRepository.save(instruction);
    }

    private void handleContainerLocationUpdates(MovementInstruction instruction, Integer userRegisterId) {
        String originBlockType = getBlockType(instruction.getOriginBlock());
        String destinationBlockType = getBlockType(instruction.getDestinationBlock());
        String destinationBlockCode = instruction.getDestinationBlock().getCode();

        if ("STACK".equals(originBlockType) && "STACK".equals(destinationBlockType)) {
            handleStackToStackMovement(instruction, userRegisterId);
        } else if ("STACK".equals(originBlockType) && ("VIRTUAL".equals(destinationBlockType) || "HEAP".equals(destinationBlockType))) {
            handleStackToVirtualOrHeapMovement(instruction, userRegisterId, destinationBlockCode);
        } else if (("VIRTUAL".equals(originBlockType) || "HEAP".equals(originBlockType)) && "STACK".equals(destinationBlockType)) {
            handleVirtualOrHeapToStackMovement(instruction, userRegisterId);
        } else if (("VIRTUAL".equals(originBlockType) || "HEAP".equals(originBlockType)) &&
                   ("VIRTUAL".equals(destinationBlockType) || "HEAP".equals(destinationBlockType))) {
            handleVirtualOrHeapToVirtualOrHeapMovement(instruction, userRegisterId);
        }
    }

    private String getBlockType(Block block) {
        if (block == null || block.getCatBlockType() == null) {
            return null;
        }
        return block.getCatBlockType().getCode();
    }

    private void handleStackToStackMovement(MovementInstruction instruction, Integer userRegisterId) {
        // Remove container from origin location (20ft)
        removeContainerFromLocation(
            instruction.getOriginBlock().getId(),
            instruction.getOriginCell().getId(),
            instruction.getOriginLevel().getId()
        );

        // Remove container from origin location (40ft)
        if (instruction.getOrigin40Block() != null) {
            removeContainerFromLocation(
                instruction.getOrigin40Block().getId(),
                instruction.getOrigin40Cell().getId(),
                instruction.getOrigin40Level().getId()
            );
        }

        // Place container in destination location (20ft)
        updateContainerLocation(
            instruction.getDestinationBlock().getId(),
            instruction.getDestinationCell().getId(),
            instruction.getDestinationLevel().getId(),
            instruction.getContainer().getId()
        );

        // Place container in destination location (40ft)
        if (instruction.getDestination40Block() != null) {
            updateContainerLocation(
                instruction.getDestination40Block().getId(),
                instruction.getDestination40Cell().getId(),
                instruction.getDestination40Level().getId(),
                instruction.getContainer().getId()
            );
        }
    }

    private void handleStackToVirtualOrHeapMovement(MovementInstruction instruction, Integer userRegisterId, String destinationBlockCode) {
        // Remove container from origin location (20ft)
        removeContainerFromLocation(
            instruction.getOriginBlock().getId(),
            instruction.getOriginCell().getId(),
            instruction.getOriginLevel().getId()
        );

        // Remove container from origin location (40ft)
        if (instruction.getOrigin40Block() != null) {
            removeContainerFromLocation(
                instruction.getOrigin40Block().getId(),
                instruction.getOrigin40Cell().getId(),
                instruction.getOrigin40Level().getId()
            );
        }

        // Only create new location if container is not going out of the yard
        if (!"Out".equals(destinationBlockCode)) {
            createContainerLocation(
                instruction.getDestinationBlock().getId(),
                instruction.getDestinationCell().getId(),
                instruction.getDestinationLevel().getId(),
                instruction.getContainer().getId(),
                userRegisterId
            );
        }
    }

    private void handleVirtualOrHeapToStackMovement(MovementInstruction instruction, Integer userRegisterId) {
        // Remove container from origin location (20ft)
        removeContainerFromLocation(
            instruction.getOriginBlock().getId(),
            instruction.getOriginCell().getId(),
            instruction.getOriginLevel().getId()
        );

        // Remove container from origin location (40ft)
        if (instruction.getOrigin40Block() != null) {
            removeContainerFromLocation(
                instruction.getOrigin40Block().getId(),
                instruction.getOrigin40Cell().getId(),
                instruction.getOrigin40Level().getId()
            );
        }

        // Place container in destination location (20ft)
        if (locationExists(instruction.getDestinationBlock().getId(), instruction.getDestinationCell().getId(), instruction.getDestinationLevel().getId())) {
            updateContainerLocation(
                instruction.getDestinationBlock().getId(),
                instruction.getDestinationCell().getId(),
                instruction.getDestinationLevel().getId(),
                instruction.getContainer().getId()
            );
        } else {
            createContainerLocation(
                instruction.getDestinationBlock().getId(),
                instruction.getDestinationCell().getId(),
                instruction.getDestinationLevel().getId(),
                instruction.getContainer().getId(),
                userRegisterId
            );
        }

        // Place container in destination location (40ft)
        if (instruction.getDestination40Block() != null) {
            if (locationExists(instruction.getDestination40Block().getId(), instruction.getDestination40Cell().getId(), instruction.getDestination40Level().getId())) {
                updateContainerLocation(
                    instruction.getDestination40Block().getId(),
                    instruction.getDestination40Cell().getId(),
                    instruction.getDestination40Level().getId(),
                    instruction.getContainer().getId()
                );
            } else {
                createContainerLocation(
                    instruction.getDestination40Block().getId(),
                    instruction.getDestination40Cell().getId(),
                    instruction.getDestination40Level().getId(),
                    instruction.getContainer().getId(),
                    userRegisterId
                );
            }
        }
    }

    private void handleVirtualOrHeapToVirtualOrHeapMovement(MovementInstruction instruction, Integer userRegisterId) {
        // Remove container from origin location
        deleteContainerLocation(instruction.getContainer().getId(), instruction.getOriginBlock().getId());

        // Create new location at destination (20ft)
        createContainerLocation(
            instruction.getDestinationBlock().getId(),
            instruction.getDestinationCell().getId(),
            instruction.getDestinationLevel().getId(),
            instruction.getContainer().getId(),
            userRegisterId
        );

        // Create new location at destination (40ft)
        if (instruction.getDestination40Block() != null) {
            createContainerLocation(
                instruction.getDestination40Block().getId(),
                instruction.getDestination40Cell().getId(),
                instruction.getDestination40Level().getId(),
                instruction.getContainer().getId(),
                userRegisterId
            );
        }
    }

    private void removeContainerFromLocation(Integer blockId, Integer cellId, Integer levelId) {
        containerLocationRepository.clearContainerFromLocation(blockId, cellId, levelId);
    }

    private void updateContainerLocation(Integer blockId, Integer cellId, Integer levelId, Integer containerId) {
        containerLocationRepository.updateContainerAtLocation(blockId, cellId, levelId, containerId);
    }

    private void createContainerLocation(Integer blockId, Integer cellId, Integer levelId, Integer containerId, Integer userRegisterId) {
        ContainerLocation location = ContainerLocation.builder()
            .block(Block.builder().id(blockId).build())
            .cell(Cell.builder().id(cellId).build())
            .level(Level.builder().id(levelId).build())
            .container(Container.builder().id(containerId).build())
            .active(true)
            .quantityRemoved(0)
            .registrationUser(User.builder().id(userRegisterId).build())
            .registrationDate(LocalDateTime.now())
            .build();

        containerLocationRepository.save(location);
    }

    private boolean locationExists(Integer blockId, Integer cellId, Integer levelId) {
        return containerLocationRepository.existsByBlockIdAndCellIdAndLevelId(blockId, cellId, levelId);
    }

    private void deleteContainerLocation(Integer containerId, Integer blockId) {
        containerLocationRepository.deleteByContainerIdAndBlockId(containerId, blockId);
    }

    private MovementInstructionConfirmOutput buildErrorOutput(Integer instructionId, String status, String message) {
        return buildFinalOutput(instructionId, status, message);
    }

    private MovementInstructionConfirmOutput buildFinalOutput(Integer instructionId, String respStatus, String respMessage) {
        // Get the final result with all location details
        MovementInstructionConfirmOutput output = movementInstructionRepository.getFinalResult(instructionId, respStatus, respMessage);
        if (output == null) {
            output = new MovementInstructionConfirmOutput();
            output.setInstruccionMovimientoId(instructionId);
        }
        output.setRespEstado(respStatus);
        output.setRespMensaje(respMessage);
        return output;
    }
}
