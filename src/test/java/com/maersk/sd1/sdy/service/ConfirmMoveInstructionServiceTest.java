package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmInput;
import com.maersk.sd1.sdy.dto.MovementInstructionConfirmOutput;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ConfirmMoveInstructionServiceTest {

    @Mock
    private MovementInstructionRepository movementInstructionRepository;

    @Mock
    private YardRepository yardRepository;

    @Mock
    private CatalogRepository catalogRepository;

    @Mock
    private ContainerLocationRepository containerLocationRepository;

    @InjectMocks
    private ConfirmMoveInstructionService service;

    private MovementInstructionConfirmInput.Input input;
    private MovementInstruction movementInstruction;

    @BeforeEach
    void setUp() {
        input = new MovementInstructionConfirmInput.Input();
        input.setMovementInstructionId(1);
        input.setUserAlias("testuser");

        // Create a basic movement instruction
        movementInstruction = MovementInstruction.builder()
            .id(1)
            .catStatus(Catalog.builder().id(1).code("DP").description("Dispatched").build())
            .container(Container.builder().id(100).build())
            .originBlock(Block.builder().id(10).catBlockType(Catalog.builder().code("STACK").build()).build())
            .originCell(Cell.builder().id(20).build())
            .originLevel(Level.builder().id(30).build())
            .destinationBlock(Block.builder().id(11).catBlockType(Catalog.builder().code("STACK").build()).code("DEST").build())
            .destinationCell(Cell.builder().id(21).build())
            .destinationLevel(Level.builder().id(31).build())
            .build();
    }

    @Test
    void testConfirmMoveInstruction_Success() {
        // Arrange
        when(yardRepository.findUsuarioIdByAlias("testuser")).thenReturn(123);
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(movementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(movementInstruction);
        
        MovementInstructionConfirmOutput expectedOutput = new MovementInstructionConfirmOutput();
        expectedOutput.setInstruccionMovimientoId(1);
        expectedOutput.setRespEstado("OK");
        expectedOutput.setRespMensaje("Movement instruction confirmed successfully");
        when(movementInstructionRepository.getFinalResult(eq(1), eq("OK"), anyString())).thenReturn(expectedOutput);

        // Act
        MovementInstructionConfirmOutput result = service.confirmMoveInstruction(input);

        // Assert
        assertNotNull(result);
        assertEquals("OK", result.getRespEstado());
        assertEquals("Movement instruction confirmed successfully", result.getRespMensaje());
        assertEquals(1, result.getInstruccionMovimientoId());

        // Verify interactions
        verify(yardRepository).findUsuarioIdByAlias("testuser");
        verify(catalogRepository).findMovementId("EXE", "EMI");
        verify(movementInstructionRepository).findById(1);
        verify(movementInstructionRepository).save(any(MovementInstruction.class));
        verify(containerLocationRepository, times(2)).clearContainerFromLocation(anyInt(), anyInt(), anyInt());
        verify(containerLocationRepository, times(2)).updateContainerAtLocation(anyInt(), anyInt(), anyInt(), anyInt());
    }

    @Test
    void testConfirmMoveInstruction_UserNotFound() {
        // Arrange
        when(yardRepository.findUsuarioIdByAlias("testuser")).thenReturn(null);

        // Act
        MovementInstructionConfirmOutput result = service.confirmMoveInstruction(input);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("User not found"));
    }

    @Test
    void testConfirmMoveInstruction_InstructionNotFound() {
        // Arrange
        when(yardRepository.findUsuarioIdByAlias("testuser")).thenReturn(123);
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.empty());

        // Act
        MovementInstructionConfirmOutput result = service.confirmMoveInstruction(input);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Movement instruction not found"));
    }

    @Test
    void testConfirmMoveInstruction_InvalidStatus() {
        // Arrange
        movementInstruction.setCatStatus(Catalog.builder().id(1).code("CR").description("Created").build());
        
        when(yardRepository.findUsuarioIdByAlias("testuser")).thenReturn(123);
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(movementInstruction));

        // Act
        MovementInstructionConfirmOutput result = service.confirmMoveInstruction(input);

        // Assert
        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("Cannot confirm an instruction"));
    }

    @Test
    void testConfirmMoveInstruction_VirtualToStackMovement() {
        // Arrange
        movementInstruction.getOriginBlock().setCatBlockType(Catalog.builder().code("VIRTUAL").build());
        movementInstruction.getDestinationBlock().setCatBlockType(Catalog.builder().code("STACK").build());
        
        when(yardRepository.findUsuarioIdByAlias("testuser")).thenReturn(123);
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(movementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(movementInstruction);
        when(containerLocationRepository.existsByBlockIdAndCellIdAndLevelId(anyInt(), anyInt(), anyInt())).thenReturn(true);
        
        MovementInstructionConfirmOutput expectedOutput = new MovementInstructionConfirmOutput();
        expectedOutput.setInstruccionMovimientoId(1);
        expectedOutput.setRespEstado("OK");
        when(movementInstructionRepository.getFinalResult(eq(1), eq("OK"), anyString())).thenReturn(expectedOutput);

        // Act
        MovementInstructionConfirmOutput result = service.confirmMoveInstruction(input);

        // Assert
        assertNotNull(result);
        assertEquals("OK", result.getRespEstado());

        // Verify that location exists check was called
        verify(containerLocationRepository).existsByBlockIdAndCellIdAndLevelId(anyInt(), anyInt(), anyInt());
        verify(containerLocationRepository).updateContainerAtLocation(anyInt(), anyInt(), anyInt(), anyInt());
    }
}
