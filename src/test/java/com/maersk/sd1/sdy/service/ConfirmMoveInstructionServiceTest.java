package com.maersk.sd1.sdy.service;

import com.maersk.sd1.common.model.*;
import com.maersk.sd1.common.repository.*;
import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionInput;
import com.maersk.sd1.sdy.dto.ConfirmMoveInstructionOutput;
import com.maersk.sd1.sdy.dto.MovementInstructionLocationDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ConfirmMoveInstructionServiceTest {

    @InjectMocks
    private ConfirmMoveInstructionService service;

    @Mock
    private MovementInstructionRepository movementInstructionRepository;
    @Mock
    private CatalogRepository catalogRepository;
    @Mock
    private ContainerRepository containerRepository;
    @Mock
    private CargoDocumentDetailRepository cargoDocumentDetailRepository;
    @Mock
    private ContainerLocationRepository containerLocationRepository;
    @Mock
    private EirRepository eirRepository;
    @Mock
    private UserRepository userRepository;

    private ConfirmMoveInstructionInput.Input validInput;
    private User testUser;
    private MovementInstruction testMovementInstruction;
    private MovementInstructionLocationDto testLocationDetails;
    private ConfirmMoveInstructionOutput mockOutput;

    @BeforeEach
    void setUp() {
        setupTestData();
    }

    private void setupTestData() {
        validInput = new ConfirmMoveInstructionInput.Input();
        validInput.setMoveInstructionId(1);
        validInput.setUserAlias("testuser");

        testUser = new User();
        testUser.setId(100);
        testUser.setAlias("testuser");
        testUser.setNames("Test User");

        testMovementInstruction = new MovementInstruction();
        testMovementInstruction.setId(1);
        testMovementInstruction.setActive(true);

        Container container = new Container();
        container.setId(200);
        testMovementInstruction.setContainer(container);

        Catalog status = new Catalog();
        status.setId(1);
        status.setCode("DP");
        status.setDescription("Dispatched");
        testMovementInstruction.setCatStatus(status);

        testLocationDetails = new MovementInstructionLocationDto(
            1, 1, 1, // origin 20ft: block, cell, level
            2, 2, 2, // origin 40ft: block, cell, level
            3, 3, 3, // destination 20ft: block, cell, level
            4, 4, 4, // destination 40ft: block, cell, level
            200, // container ID
            "DP", // status code
            "Dispatched", // status description
            "STACK", // destination block type
            "STACK", // origin block type
            "A1" // destination block code
        );

        mockOutput = new ConfirmMoveInstructionOutput();
        mockOutput.setInstruccionMovimientoId(1);
        mockOutput.setRespEstado("SUCCESS");
        mockOutput.setRespMensaje("Movement instruction confirmed successfully");
    }

    @Test
    void confirmMoveInstruction_withValidStackToStackMovement_shouldReturnSuccess() {
        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(testLocationDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("SUCCESS"), anyString())).thenReturn(mockOutput);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        assertEquals("Movement instruction confirmed successfully", result.getRespMensaje());
        verify(movementInstructionRepository).save(any(MovementInstruction.class));
        verify(containerLocationRepository).clearContainerFromLocation(1, 1, 1);
        verify(containerLocationRepository).clearContainerFromLocation(2, 2, 2);
        verify(containerLocationRepository).updateContainerAtLocation(3, 3, 3, 200);
        verify(containerLocationRepository).updateContainerAtLocation(4, 4, 4, 200);
    }

    @Test
    void confirmMoveInstruction_withNullMoveInstructionId_shouldReturnError() {
        validInput.setMoveInstructionId(null);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Move instruction ID cannot be null", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withNullUserAlias_shouldReturnError() {
        validInput.setUserAlias(null);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("User alias cannot be null or empty", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withEmptyUserAlias_shouldReturnError() {
        validInput.setUserAlias("");

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("User alias cannot be null or empty", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withMovementInstructionDetailsNotFound_shouldReturnError() {
        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(null);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("BUSINESS_ERROR"), anyString()))
            .thenReturn(createErrorOutput("Movement instruction not found with ID: 1"));

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Movement instruction not found with ID: 1", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withMovementInstructionEntityNotFound_shouldReturnError() {
        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(testLocationDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.empty());
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("BUSINESS_ERROR"), anyString()))
            .thenReturn(createErrorOutput("Movement instruction entity not found"));
        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertEquals("Movement instruction entity not found", result.getRespMensaje());
    }

    @Test
    void confirmMoveInstruction_withInvalidStatus_shouldReturnError() {
        MovementInstructionLocationDto invalidStatusDetails = new MovementInstructionLocationDto(
            1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 200,
            "CR", "Created", "STACK", "STACK", "A1"
        );

        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(invalidStatusDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("BUSINESS_ERROR"), anyString()))
            .thenReturn(createErrorOutput("The selected movement instruction is in status Created. You cannot confirm an instruction in status CREATED, TO BE ATTENDED, IN PROGRESS, or ON HOLD"));

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("BUSINESS_ERROR", result.getRespEstado());
        assertTrue(result.getRespMensaje().contains("You cannot confirm an instruction in status CREATED, TO BE ATTENDED, IN PROGRESS, or ON HOLD"));
    }

    @Test
    void confirmMoveInstruction_withStackToVirtualMovement_shouldReturnSuccess() {
        MovementInstructionLocationDto stackToVirtualDetails = new MovementInstructionLocationDto(
            1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 200,
            "DP", "Dispatched", "VIRTUAL", "STACK", "VRT1"
        );

        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(stackToVirtualDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("SUCCESS"), anyString())).thenReturn(mockOutput);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        verify(containerLocationRepository).clearContainerFromLocation(1, 1, 1);
        verify(containerLocationRepository).clearContainerFromLocation(2, 2, 2);
        verify(containerLocationRepository).save(any(ContainerLocation.class));
    }

    @Test
    void confirmMoveInstruction_withStackToVirtualMovementToOut_shouldNotCreateLocation() {
        MovementInstructionLocationDto stackToOutDetails = new MovementInstructionLocationDto(
            1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 200,
            "DP", "Dispatched", "VIRTUAL", "STACK", "Out"
        );

        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(stackToOutDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("SUCCESS"), anyString())).thenReturn(mockOutput);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);
        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        verify(containerLocationRepository).clearContainerFromLocation(1, 1, 1);
        verify(containerLocationRepository).clearContainerFromLocation(2, 2, 2);
        verify(containerLocationRepository, never()).save(any(ContainerLocation.class));
    }

    @Test
    void confirmMoveInstruction_withVirtualToStackMovement_shouldReturnSuccess() {
        MovementInstructionLocationDto virtualToStackDetails = new MovementInstructionLocationDto(
            1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 200,
            "DP", "Dispatched", "STACK", "VIRTUAL", "A1"
        );

        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(virtualToStackDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(containerLocationRepository.countByBlockCellLevel(3, 3, 3)).thenReturn(1L);
        when(containerLocationRepository.countByBlockCellLevel(4, 4, 4)).thenReturn(0L);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("SUCCESS"), anyString())).thenReturn(mockOutput);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        verify(containerLocationRepository).clearContainerFromLocation(1, 1, 1);
        verify(containerLocationRepository).clearContainerFromLocation(2, 2, 2);
        verify(containerLocationRepository).updateContainerAtLocation(3, 3, 3, 200);
        verify(containerLocationRepository).save(any(ContainerLocation.class)); // For 40ft location
    }

    @Test
    void confirmMoveInstruction_withVirtualToVirtualMovement_shouldReturnSuccess() {
        MovementInstructionLocationDto virtualToVirtualDetails = new MovementInstructionLocationDto(
            1, 1, 1, 2, 2, 2, 3, 3, 3, 4, 4, 4, 200,
            "DP", "Dispatched", "VIRTUAL", "VIRTUAL", "VRT2"
        );

        when(userRepository.findByUserAlias("testuser")).thenReturn(Optional.of(testUser));
        when(catalogRepository.findMovementId("EXE", "EMI")).thenReturn(2);
        when(movementInstructionRepository.findLocationDtoByInstructionId(1)).thenReturn(virtualToVirtualDetails);
        when(movementInstructionRepository.findById(1)).thenReturn(Optional.of(testMovementInstruction));
        when(movementInstructionRepository.save(any(MovementInstruction.class))).thenReturn(testMovementInstruction);
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), eq("SUCCESS"), anyString())).thenReturn(mockOutput);

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        assertEquals("SUCCESS", result.getRespEstado());
        verify(containerLocationRepository).deleteByContainerIdAndBlockId(200, 1);
        verify(containerLocationRepository, times(2)).save(any(ContainerLocation.class));
    }

    @Test
    void confirmMoveInstruction_withException_shouldReturnError() {
        when(userRepository.findByUserAlias("testuser")).thenThrow(new RuntimeException("Database error"));
        when(containerLocationRepository.findConfirmMoveInstructionOutput(eq(1), anyString(), anyString()))
            .thenReturn(createErrorOutput("Internal error occurred"));

        ConfirmMoveInstructionOutput result = service.confirmMoveInstruction(validInput);

        assertNotNull(result);
        verify(containerLocationRepository).findConfirmMoveInstructionOutput(eq(1), anyString(), anyString());
    }

    private ConfirmMoveInstructionOutput createErrorOutput(String message) {
        ConfirmMoveInstructionOutput output = new ConfirmMoveInstructionOutput();
        output.setInstruccionMovimientoId(1);
        output.setRespEstado("BUSINESS_ERROR");
        output.setRespMensaje(message);
        return output;
    }
}
